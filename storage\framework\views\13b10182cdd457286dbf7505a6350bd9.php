<?php $__env->startSection('title', 'Gestion des fournisseurs'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- En-tête responsive -->
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3 mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-industry me-2"></i>Gestion des fournisseurs
        </h1>
        <a href="<?php echo e(route('accountant.suppliers.create')); ?>" class="btn btn-primary w-100 w-md-auto">
            <i class="fas fa-plus me-2"></i>Nouveau fournisseur
        </a>
    </div>

    <!-- Liste des fournisseurs -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <div class="row g-3">
                <!-- Titre et recherche -->
                <div class="col-12 col-md-6">
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               id="searchInput" 
                               placeholder="Rechercher un fournisseur..."
                               value="<?php echo e(request('search')); ?>">
                    </div>
                </div>
                <!-- Filtres -->
                <div class="col-12 col-md-4">
                    <select class="form-select" id="statusFilter">
                        <option value="">Tous les statuts</option>
                        <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Actif</option>
                        <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactif</option>
                    </select>
                </div>
                <!-- Bouton réinitialiser -->
                <div class="col-12 col-md-2">
                    <button type="button" class="btn btn-secondary w-100" id="resetFilters">
                        <i class="fas fa-undo me-1"></i> Réinitialiser
                    </button>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="text-nowrap">Nom</th>
                            <th class="text-nowrap d-none d-md-table-cell">Contact</th>
                            <th class="text-nowrap">Email/Tél</th>
                            <th class="text-nowrap d-none d-lg-table-cell">Adresse</th>
                            <th class="text-nowrap">Statut</th>
                            <th class="text-nowrap text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-light rounded p-2 me-3 d-none d-sm-block">
                                            <i class="fas fa-building text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?php echo e($supplier->name); ?></h6>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">
                                    <?php if($supplier->contact_person): ?>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user-tie text-info me-2"></i>
                                            <?php echo e($supplier->contact_person); ?>

                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Non spécifié</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex flex-column gap-1">
                                        <?php if($supplier->email): ?>
                                            <div class="small">
                                                <i class="fas fa-envelope text-muted me-2"></i>
                                                <?php echo e($supplier->email); ?>

                                            </div>
                                        <?php endif; ?>
                                        <?php if($supplier->phone): ?>
                                            <div class="small">
                                                <i class="fas fa-phone text-muted me-2"></i>
                                                <?php echo e($supplier->phone); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="d-none d-lg-table-cell">
                                    <?php if($supplier->address): ?>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                            <?php echo e($supplier->address); ?>

                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Non spécifié</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($supplier->is_active): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check-circle me-1"></i>Actif
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Inactif
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex gap-2 justify-content-end">
                                        <a href="<?php echo e(route('accountant.suppliers.edit', $supplier)); ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                            <span class="d-none d-md-inline ms-1">Modifier</span>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-danger btn-sm"
                                                onclick="confirmDelete('<?php echo e($supplier->id); ?>')">
                                            <i class="fas fa-trash"></i>
                                            <span class="d-none d-md-inline ms-1">Supprimer</span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center gap-3">
                                        <i class="fas fa-inbox fa-3x text-muted"></i>
                                        <p class="mb-0">Aucun fournisseur trouvé</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination responsive -->
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center gap-3 mt-4">
                <div class="text-muted small">
                    Affichage de <?php echo e($suppliers->firstItem() ?? 0); ?> à <?php echo e($suppliers->lastItem() ?? 0); ?> sur <?php echo e($suppliers->total()); ?> fournisseurs
                </div>
                <div class="d-flex justify-content-center w-100 w-md-auto">
                    <?php echo e($suppliers->links()); ?>

                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
    /* Styles de base */
    .table th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8125rem;
        letter-spacing: 0.5px;
    }

    .badge {
        font-weight: 500;
        padding: 0.5em 0.75em;
    }

    .table td {
        vertical-align: middle;
        padding: 0.75rem;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .table td, .table th {
            font-size: 0.875rem;
            padding: 0.5rem;
        }

        .badge {
            font-size: 0.75rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .card-header .row {
            margin-bottom: -1rem;
        }

        .card-header .col-12 {
            margin-bottom: 1rem;
        }
    }

    /* Animation du chargement */
    .table-responsive.loading {
        position: relative;
        min-height: 200px;
    }

    .table-responsive.loading::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .table-responsive.loading::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 1001;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const resetButton = document.getElementById('resetFilters');
    const tableResponsive = document.querySelector('.table-responsive');

    let timer;

    // Fonction pour appliquer les filtres
    function applyFilters() {
        tableResponsive.classList.add('loading');
        
        const searchValue = searchInput.value;
        const statusValue = statusFilter.value;

        // Construire l'URL avec les paramètres
        const params = new URLSearchParams(window.location.search);
        
        if (searchValue) params.set('search', searchValue);
        else params.delete('search');
        
        if (statusValue) params.set('status', statusValue);
        else params.delete('status');

        // Rediriger avec les nouveaux paramètres
        window.location.href = `${window.location.pathname}?${params.toString()}`;
    }

    // Gestionnaires d'événements avec debounce pour la recherche
    searchInput.addEventListener('input', function() {
        clearTimeout(timer);
        timer = setTimeout(applyFilters, 500);
    });

    // Changement immédiat pour les select
    statusFilter.addEventListener('change', applyFilters);

    // Réinitialisation des filtres
    resetButton.addEventListener('click', function() {
        window.location.href = window.location.pathname;
    });

    // Initialiser les tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function confirmDelete(supplierId) {
    Swal.fire({
        title: 'Êtes-vous sûr ?',
        text: "Cette action est irréversible !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Oui, supprimer',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `<?php echo e(url('accountant/suppliers')); ?>/${supplierId}`;
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);
            
            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';
            form.appendChild(methodField);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/suppliers/index.blade.php ENDPATH**/ ?>