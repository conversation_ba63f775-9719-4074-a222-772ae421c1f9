<?php $__env->startSection('title', 'Détails du fournisseur'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-industry me-2"></i>Détails du fournisseur
        </h1>
        <div>
            <a href="<?php echo e(route('accountant.suppliers.edit', $supplier)); ?>" class="btn btn-primary shadow-sm">
                <i class="fas fa-edit me-2"></i>Modifier
            </a>
            <button type="button" class="btn btn-danger shadow-sm" onclick="confirmDelete('<?php echo e($supplier->id); ?>')">
                <i class="fas fa-trash me-2"></i>Supprimer
            </button>
            <form id="delete-form-<?php echo e($supplier->id); ?>" 
                  action="<?php echo e(route('accountant.suppliers.destroy', $supplier)); ?>" 
                  method="POST" 
                  style="display: none;">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
            </form>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>

    <!-- Informations du fournisseur -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-info-circle me-2"></i>Informations du fournisseur
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table">
                        <tr>
                            <th style="width: 200px;">Nom :</th>
                            <td><?php echo e($supplier->name); ?></td>
                        </tr>
                        <tr>
                            <th>Personne de contact :</th>
                            <td><?php echo e($supplier->contact_person ?? 'Non renseigné'); ?></td>
                        </tr>
                        <tr>
                            <th>Email :</th>
                            <td><?php echo e($supplier->email ?? 'Non renseigné'); ?></td>
                        </tr>
                        <tr>
                            <th>Téléphone :</th>
                            <td><?php echo e($supplier->phone); ?></td>
                        </tr>
                        <tr>
                            <th>Adresse :</th>
                            <td><?php echo e($supplier->address ?? 'Non renseigné'); ?></td>
                        </tr>
                        <tr>
                            <th>Statut :</th>
                            <td>
                                <?php if($supplier->is_active): ?>
                                    <span class="badge bg-success">Actif</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactif</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des approvisionnements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-history me-2"></i>Historique des approvisionnements
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="suppliesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Produit</th>
                            <th>Quantité</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $supplier->supplies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($supply->reference); ?></td>
                                <td><?php echo e($supply->date->format('d/m/Y')); ?></td>
                                <td><?php echo e($supply->product->name); ?></td>
                                <td><?php echo e(number_format($supply->total_tonnage, 2, ',', ' ')); ?></td>
                                <td>
                                    <?php if($supply->status === 'pending'): ?>
                                        <span class="badge bg-warning">En attente</span>
                                    <?php elseif($supply->status === 'validated'): ?>
                                        <span class="badge bg-success">Validé</span>
                                    <?php elseif($supply->status === 'rejected'): ?>
                                        <span class="badge bg-danger">Rejeté</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('accountant.supplies.show', $supply)); ?>" 
                                       class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="text-center">Aucun approvisionnement trouvé</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmDelete(supplierId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')) {
        document.getElementById('delete-form-' + supplierId).submit();
    }
}

$(document).ready(function() {
    $('#suppliesTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/fr-FR.json',
        },
        pageLength: 10,
        "order": [[1, "desc"]],
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/suppliers/show.blade.php ENDPATH**/ ?>