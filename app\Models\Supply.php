<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;
use App\Models\Category;
use App\Models\Supplier;
use App\Models\SupplyDetail;
use App\Models\SupplyItem;
use App\Models\City;
use App\Models\SupplyNotification;
use App\Models\SupplyCity;

class Supply extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'supplies';

    /**
     * Les attributs qui peuvent être assignés en masse.
     *
     * @var array<string>
     */
    protected $fillable = [
        'reference',
        'supplier_id',
        'category_id',
        'date',
        'expected_delivery_date',
        'invoice_file',
        'notes',
        'total_tonnage',
        'total_amount',
        'total_remaining',
        'status',
        'rejection_reason',
        'created_by',
        'validator_id',
        'validated_at'
    ];

    /**
     * Les attributs qui doivent être convertis en dates.
     *
     * @var array<string>
     */
    protected $casts = [
        'date' => 'date',
        'expected_delivery_date' => 'date',
        'validated_at' => 'datetime',
        'total_tonnage' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'total_remaining' => 'decimal:2'
    ];

    /**
     * Obtenir la catégorie associée à cet approvisionnement.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Obtenir l'utilisateur qui a créé l'approvisionnement.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Obtenir l'utilisateur qui a validé l'approvisionnement.
     */
    public function validator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'validator_id');
    }

    /**
     * Obtenir le fournisseur associé à cet approvisionnement.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Obtenir les détails de l'approvisionnement.
     */
    public function details(): HasMany
    {
        return $this->hasMany(SupplyDetail::class);
    }

    /**
     * Obtenir les éléments de l'approvisionnement.
     */
    public function items(): HasMany
    {
        return $this->hasMany(SupplyItem::class);
    }

    /**
     * Obtenir les villes associées à cet approvisionnement via la table pivot.
     */
    public function cities(): HasMany
    {
        return $this->hasMany(SupplyCity::class);
    }

    /**
     * Obtenir les notifications associées à cet approvisionnement.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(SupplyNotification::class);
    }
}
