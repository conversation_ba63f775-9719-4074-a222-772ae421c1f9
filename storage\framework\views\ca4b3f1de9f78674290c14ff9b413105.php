<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des véhicules</h1>
        <a href="<?php echo e(route('accountant.trucks.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Ajouter un véhicule
        </a>
    </div>

    <!-- Liste des véhicules -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Liste des véhicules</h6>
        </div>
        <div class="card-body">
            <?php if(session('success')): ?>
                <div class="alert alert-success">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Immatriculation</th>
                            <th>Marque</th>
                            <th>Modèle</th>
                            <th>Capacité</th>
                            <th>Année</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $trucks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $truck): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="<?php echo e($truck->status === 'maintenance' ? 'table-warning' : ($truck->status === 'busy' ? 'table-info' : '')); ?>">
                                <td class="fw-bold"><?php echo e($truck->registration_number); ?></td>
                                <td><?php echo e($truck->brand); ?></td>
                                <td><?php echo e($truck->model); ?></td>
                                <td class="text-end fw-bold <?php echo e($truck->capacity_value >= 20 ? 'table-success' : ''); ?>">
                                    <?php if($truck->capacity_name): ?>
                                        <?php echo e($truck->capacity_name); ?> (<?php echo e(number_format($truck->capacity_value, 1)); ?> <?php echo e($truck->capacity_unit); ?>)
                                    <?php else: ?>
                                        Non définie
                                    <?php endif; ?>
                                </td>
                                <td class="text-center <?php echo e((date('Y') - $truck->year) > 10 ? 'table-danger' : ''); ?>">
                                    <?php echo e($truck->year); ?>

                                </td>
                                <td>
                                    <?php
                                        $statusClass = match($truck->status) {
                                            'available' => 'success',
                                            'maintenance' => 'warning',
                                            'busy' => 'info',
                                            'assigned' => 'primary',
                                            default => 'secondary'
                                        };
                                        $statusLabel = match($truck->status) {
                                            'available' => 'Disponible',
                                            'maintenance' => 'En maintenance',
                                            'busy' => 'En mission',
                                            'assigned' => $truck->driver ? 'Affecté à ' . $truck->driver->full_name : 'Affecté',
                                            default => 'Statut inconnu'
                                        };
                                    ?>
                                    <span class="badge bg-<?php echo e($statusClass); ?> text-wrap">
                                        <?php echo e($statusLabel); ?>

                                    </span>
                                    <?php if($truck->driver && $truck->status === 'assigned'): ?>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i><?php echo e($truck->driver->full_name); ?>

                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('accountant.trucks.show', $truck)); ?>" 
                                       class="btn btn-info btn-sm"
                                       title="Voir les détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center">Aucun véhicule trouvé</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4">
                <?php echo e($trucks->links()); ?>

            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
    .table td {
        vertical-align: middle;
    }
    .badge {
        font-size: 0.85rem;
        padding: 0.5em 0.75em;
    }
    .btn-group {
        gap: 0.25rem;
    }
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/trucks/index.blade.php ENDPATH**/ ?>