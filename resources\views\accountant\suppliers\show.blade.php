@extends('layouts.accountant')

@section('title', 'Détails du fournisseur')

@section('content')
<div class="container-fluid">
    <!-- En-tête -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-industry me-2"></i>Détails du fournisseur
        </h1>
        <div>
            <a href="{{ route('accountant.suppliers.edit', $supplier) }}" class="btn btn-primary shadow-sm">
                <i class="fas fa-edit me-2"></i>Modifier
            </a>
            <button type="button" class="btn btn-danger shadow-sm" onclick="confirmDelete('{{ $supplier->id }}')">
                <i class="fas fa-trash me-2"></i>Supprimer
            </button>
            <form id="delete-form-{{ $supplier->id }}" 
                  action="{{ route('accountant.suppliers.destroy', $supplier) }}" 
                  method="POST" 
                  style="display: none;">
                @csrf
                @method('DELETE')
            </form>
        </div>
    </div>

    @if (session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <!-- Informations du fournisseur -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-info-circle me-2"></i>Informations du fournisseur
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table">
                        <tr>
                            <th style="width: 200px;">Nom :</th>
                            <td>{{ $supplier->name }}</td>
                        </tr>
                        <tr>
                            <th>Personne de contact :</th>
                            <td>{{ $supplier->contact_person ?? 'Non renseigné' }}</td>
                        </tr>
                        <tr>
                            <th>Email :</th>
                            <td>{{ $supplier->email ?? 'Non renseigné' }}</td>
                        </tr>
                        <tr>
                            <th>Téléphone :</th>
                            <td>{{ $supplier->phone }}</td>
                        </tr>
                        <tr>
                            <th>Adresse :</th>
                            <td>{{ $supplier->address ?? 'Non renseigné' }}</td>
                        </tr>
                        <tr>
                            <th>Statut :</th>
                            <td>
                                @if($supplier->is_active)
                                    <span class="badge bg-success">Actif</span>
                                @else
                                    <span class="badge bg-danger">Inactif</span>
                                @endif
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des approvisionnements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-history me-2"></i>Historique des approvisionnements
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="suppliesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Produit</th>
                            <th>Quantité</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($supplier->supplies as $supply)
                            <tr>
                                <td>{{ $supply->reference }}</td>
                                <td>{{ $supply->date->format('d/m/Y') }}</td>
                                <td>
                                    @if($supply->details && $supply->details->count() > 0)
                                        @foreach($supply->details as $detail)
                                            @if($detail->product)
                                                <span class="badge bg-primary me-1">{{ $detail->product->name }}</span>
                                            @endif
                                        @endforeach
                                    @elseif($supply->items && $supply->items->count() > 0)
                                        @foreach($supply->items as $item)
                                            @if($item->product)
                                                <span class="badge bg-primary me-1">{{ $item->product->name }}</span>
                                            @endif
                                        @endforeach
                                    @else
                                        <span class="text-muted">Aucun produit</span>
                                    @endif
                                </td>
                                <td>{{ number_format($supply->total_tonnage, 2, ',', ' ') }} T</td>
                                <td>
                                    @if($supply->status === 'pending')
                                        <span class="badge bg-warning">En attente</span>
                                    @elseif($supply->status === 'validated')
                                        <span class="badge bg-success">Validé</span>
                                    @elseif($supply->status === 'rejected')
                                        <span class="badge bg-danger">Rejeté</span>
                                    @else
                                        <span class="badge bg-secondary">{{ ucfirst($supply->status) }}</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('accountant.supplies.show', $supply) }}"
                                       class="btn btn-info btn-sm"
                                       data-bs-toggle="tooltip"
                                       title="Voir les détails">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">Aucun approvisionnement trouvé</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(supplierId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')) {
        document.getElementById('delete-form-' + supplierId).submit();
    }
}

$(document).ready(function() {
    $('#suppliesTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/fr-FR.json',
        },
        pageLength: 10,
        "order": [[1, "desc"]],
    });
});
</script>
@endpush
