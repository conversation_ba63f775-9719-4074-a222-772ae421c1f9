@extends('layouts.accountant')

@push('styles')
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --info-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --maintenance-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --available-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-trucks-page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.page-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.page-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
}

.title-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.title-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin-top: 0.5rem;
    font-weight: 300;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.modern-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary-modern {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-success-modern {
    background: var(--warning-gradient);
    color: white;
}

.btn-success-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4);
    color: white;
}

.btn-info-modern {
    background: var(--info-gradient);
    color: white;
}

.btn-info-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(250, 112, 154, 0.4);
    color: white;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-card-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stats-info h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #2d3748;
}

.stats-info p {
    color: #718096;
    margin: 0;
    font-weight: 500;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.icon-total { background: var(--primary-gradient); }
.icon-available { background: var(--success-gradient); }
.icon-assigned { background: var(--info-gradient); }
.icon-maintenance { background: var(--danger-gradient); }

.modern-alert {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    color: #155724;
    border-left: 4px solid #28a745;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideInDown 0.5s ease-out;
}

.alert-icon {
    width: 40px;
    height: 40px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.main-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.content-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.content-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
}

.search-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
}

.modern-search {
    position: relative;
    min-width: 300px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: var(--transition);
    background: white;
}

.search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1rem;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
}

.toggle-btn {
    background: none;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    color: #6c757d;
    transition: var(--transition);
    cursor: pointer;
}

.toggle-btn.active {
    background: white;
    color: #495057;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-btn:hover {
    color: #495057;
}
</style>
@endpush

@section('content')
<div class="modern-trucks-page">
    <div class="container-fluid">
        <!-- En-tête moderne -->
        <div class="page-header">
            <div class="page-title">
                <div class="title-content">
                    <h1>
                        <div class="title-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        Gestion des Véhicules
                    </h1>
                    <p class="title-subtitle">Gérez votre flotte de véhicules et leur statut</p>
                </div>
                <div class="action-buttons">
                    <a href="{{ route('accountant.drivers.index') }}" class="modern-btn btn-info-modern">
                        <i class="fas fa-user-tie"></i>
                        Liste des chauffeurs
                    </a>
                    <a href="{{ route('accountant.trucks.create') }}" class="modern-btn btn-primary-modern">
                        <i class="fas fa-plus"></i>
                        Nouveau Véhicule
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages de notification -->
        @if(session('success'))
        <div class="modern-alert">
            <div class="alert-icon">
                <i class="fas fa-check"></i>
            </div>
            <div>
                <strong>Succès !</strong>
                {{ session('success') }}
            </div>
        </div>
        @endif

        <!-- Statistiques -->
        <div class="stats-section">
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $trucks->count() }}</h3>
                        <p>Total Véhicules</p>
                    </div>
                    <div class="stats-icon icon-total">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $trucks->where('status', 'available')->count() }}</h3>
                        <p>Disponibles</p>
                    </div>
                    <div class="stats-icon icon-available">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $trucks->where('status', 'assigned')->count() }}</h3>
                        <p>Assignés</p>
                    </div>
                    <div class="stats-icon icon-assigned">
                        <i class="fas fa-user-check"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $trucks->where('status', 'maintenance')->count() }}</h3>
                        <p>En Maintenance</p>
                    </div>
                    <div class="stats-icon icon-maintenance">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <div class="content-header">
                <h2 class="content-title">
                    <i class="fas fa-list"></i>
                    Liste des Véhicules
                </h2>
                <div class="search-container">
                    <div class="modern-search">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Rechercher un véhicule..." id="searchInput">
                    </div>
                    <div class="view-toggle">
                        <button class="toggle-btn active" data-view="cards">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="toggle-btn" data-view="table">
                            <i class="fas fa-table"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Vue en cartes -->
            <div id="cards-view" class="p-4">
                <div class="trucks-grid">
                    @forelse($trucks as $truck)
                        @php
                            $statusClass = match($truck->status) {
                                'available' => 'status-available',
                                'maintenance' => 'status-maintenance',
                                'busy' => 'status-busy',
                                'assigned' => 'status-assigned',
                                default => 'status-unknown'
                            };
                            $statusLabel = match($truck->status) {
                                'available' => 'Disponible',
                                'maintenance' => 'En maintenance',
                                'busy' => 'En mission',
                                'assigned' => 'Assigné',
                                default => 'Statut inconnu'
                            };
                            $statusIcon = match($truck->status) {
                                'available' => 'fas fa-check-circle',
                                'maintenance' => 'fas fa-tools',
                                'busy' => 'fas fa-road',
                                'assigned' => 'fas fa-user-check',
                                default => 'fas fa-question-circle'
                            };
                        @endphp
                        <div class="truck-card" data-search="{{ strtolower($truck->registration_number . ' ' . $truck->brand . ' ' . $truck->model . ' ' . ($truck->capacity_name ?? '') . ' ' . $truck->year) }}">
                            <div class="truck-card-header">
                                <div class="truck-icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="truck-status">
                                    <span class="status-badge {{ $statusClass }}">
                                        <i class="{{ $statusIcon }}"></i>
                                        {{ $statusLabel }}
                                    </span>
                                </div>
                            </div>
                            <div class="truck-card-body">
                                <h3 class="truck-registration">{{ $truck->registration_number }}</h3>
                                <div class="truck-info">
                                    <div class="info-row">
                                        <div class="info-item">
                                            <i class="fas fa-industry"></i>
                                            <span>{{ $truck->brand }}</span>
                                        </div>
                                        <div class="info-item">
                                            <i class="fas fa-car"></i>
                                            <span>{{ $truck->model }}</span>
                                        </div>
                                    </div>
                                    <div class="info-row">
                                        <div class="info-item">
                                            <i class="fas fa-weight-hanging"></i>
                                            <span>
                                                @if($truck->capacity_name)
                                                    {{ $truck->capacity_name }}
                                                    <small>({{ number_format($truck->capacity_value, 1) }} {{ $truck->capacity_unit }})</small>
                                                @else
                                                    <span class="text-muted">Non définie</span>
                                                @endif
                                            </span>
                                        </div>
                                        <div class="info-item">
                                            <i class="fas fa-calendar"></i>
                                            <span class="{{ (date('Y') - $truck->year) > 10 ? 'text-warning' : '' }}">
                                                {{ $truck->year }}
                                                @if((date('Y') - $truck->year) > 10)
                                                    <small class="text-warning">(Ancien)</small>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                    @if($truck->driver && $truck->status === 'assigned')
                                        <div class="driver-info">
                                            <i class="fas fa-user"></i>
                                            <span>Chauffeur: <strong>{{ $truck->driver->full_name }}</strong></span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="truck-card-footer">
                                <a href="{{ route('accountant.trucks.show', $truck) }}" class="action-btn btn-view" title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('accountant.trucks.edit', $truck) }}" class="action-btn btn-edit" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="action-btn btn-status" title="Changer le statut">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                            </div>
                        </div>
                    @empty
                        <div class="empty-state-modern">
                            <div class="empty-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <h3>Aucun véhicule enregistré</h3>
                            <p>Commencez par ajouter votre premier véhicule à la flotte</p>
                            <a href="{{ route('accountant.trucks.create') }}" class="modern-btn btn-primary-modern">
                                <i class="fas fa-plus"></i>
                                Ajouter un véhicule
                            </a>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Vue en tableau -->
            <div id="table-view" class="p-4" style="display: none;">
                <div class="modern-table-container">
                    <table class="modern-table" id="trucksTable">
                        <thead>
                            <tr>
                                <th>Immatriculation</th>
                                <th>Marque & Modèle</th>
                                <th>Capacité</th>
                                <th>Année</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($trucks as $truck)
                                @php
                                    $statusClass = match($truck->status) {
                                        'available' => 'status-available',
                                        'maintenance' => 'status-maintenance',
                                        'busy' => 'status-busy',
                                        'assigned' => 'status-assigned',
                                        default => 'status-unknown'
                                    };
                                    $statusLabel = match($truck->status) {
                                        'available' => 'Disponible',
                                        'maintenance' => 'En maintenance',
                                        'busy' => 'En mission',
                                        'assigned' => 'Assigné',
                                        default => 'Statut inconnu'
                                    };
                                    $statusIcon = match($truck->status) {
                                        'available' => 'fas fa-check-circle',
                                        'maintenance' => 'fas fa-tools',
                                        'busy' => 'fas fa-road',
                                        'assigned' => 'fas fa-user-check',
                                        default => 'fas fa-question-circle'
                                    };
                                @endphp
                                <tr data-search="{{ strtolower($truck->registration_number . ' ' . $truck->brand . ' ' . $truck->model . ' ' . ($truck->capacity_name ?? '') . ' ' . $truck->year) }}">
                                    <td>
                                        <div class="table-registration">
                                            <i class="fas fa-truck"></i>
                                            <strong>{{ $truck->registration_number }}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="table-vehicle-info">
                                            <div class="vehicle-brand">{{ $truck->brand }}</div>
                                            <div class="vehicle-model">{{ $truck->model }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="capacity-info">
                                            @if($truck->capacity_name)
                                                <div class="capacity-name">{{ $truck->capacity_name }}</div>
                                                <div class="capacity-value">{{ number_format($truck->capacity_value, 1) }} {{ $truck->capacity_unit }}</div>
                                            @else
                                                <span class="text-muted">Non définie</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="year-info {{ (date('Y') - $truck->year) > 10 ? 'old-vehicle' : '' }}">
                                            <i class="fas fa-calendar"></i>
                                            {{ $truck->year }}
                                            @if((date('Y') - $truck->year) > 10)
                                                <small class="old-badge">Ancien</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="status-info">
                                            <span class="table-badge {{ $statusClass }}">
                                                <i class="{{ $statusIcon }}"></i>
                                                {{ $statusLabel }}
                                            </span>
                                            @if($truck->driver && $truck->status === 'assigned')
                                                <div class="driver-name">
                                                    <i class="fas fa-user"></i>
                                                    {{ $truck->driver->full_name }}
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="{{ route('accountant.trucks.show', $truck) }}" class="table-action-btn btn-view" title="Voir les détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('accountant.trucks.edit', $truck) }}" class="table-action-btn btn-edit" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="table-action-btn btn-status" title="Changer le statut">
                                                <i class="fas fa-exchange-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6">
                                        <div class="empty-table">
                                            <i class="fas fa-truck"></i>
                                            <h4>Aucun véhicule enregistré</h4>
                                            <p>Commencez par ajouter votre premier véhicule</p>
                                            <a href="{{ route('accountant.trucks.create') }}" class="modern-btn btn-primary-modern">
                                                <i class="fas fa-plus"></i>
                                                Ajouter un véhicule
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($trucks->hasPages())
                <div class="modern-pagination">
                    {{ $trucks->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .table td {
        vertical-align: middle;
    }
    .badge {
        font-size: 0.85rem;
        padding: 0.5em 0.75em;
    }
    .btn-group {
        gap: 0.25rem;
    }
</style>
@endpush
@endsection
